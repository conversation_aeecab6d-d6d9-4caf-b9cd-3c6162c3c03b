<?php
defined('BASEPATH') or exit('No direct script access allowed');

class IdrgModel extends CI_Model
{

    function __construct(){
		parent::__construct();
        $this->load->database();
	}


   /**
    * Update claim data with response
    * @param array $data Data to update
    * @param int $id Record ID
    * @return bool Success status
    */
   public function editNewClaim($data, $id)
   {
       try {
           $this->db->where('ID', $id);
           $result = $this->db->update('medicalrecord.bridging_klaim', $data);

           if ($result === FALSE) {
               log_message('error', 'Database update failed in editNewClaim: ' . $this->db->error()['message']);
               return false;
           }

           return true;
       } catch (Exception $e) {
           log_message('error', 'Exception in editNewClaim: ' . $e->getMessage());
           return false;
       }
   }

    public function dataNewClaim($nopen)
	{
		// Add error handling and parameter binding for security
		try {
			// Use parameter binding to prevent SQL injection
			$query = "SELECT * FROM medicalrecord.bridging_klaim WHERE NOPEN = ? AND JENIS_BRIDGING = 1";
			$bind = $this->db->query($query, array($nopen));

			// Check if query was successful
			if ($bind === FALSE) {
				log_message('error', 'Database query failed in dataNewClaim: ' . $this->db->error()['message']);
				return array(); // Return empty array on error
			}

			return $bind->result_array();
		} catch (Exception $e) {
			log_message('error', 'Exception in dataNewClaim: ' . $e->getMessage());
			return array(); // Return empty array on error
		}
	}



}